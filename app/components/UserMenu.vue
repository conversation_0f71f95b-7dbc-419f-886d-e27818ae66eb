<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui';

const { signOut, data } = useAuth();

const userName = computed(() => data.value?.user?.name);

const items = ref<DropdownMenuItem[][]>([
    [
        {
            label: userName.value || 'Unknown User',
            avatar: {
                src: 'https://github.com/benjamincanac.png',
            },
            type: 'label',
        },
    ],
    [
        {
            class: 'cursor-pointer',
            label: 'Profile',
            icon: 'i-lucide-user',
        },
        {
            class: 'cursor-pointer',
            label: 'Settings',
            icon: 'i-lucide-cog',
            kbds: [','],
        },
    ],
    [
        {
            class: 'cursor-pointer',
            label: 'Logout',
            icon: 'i-lucide-log-out',
            onSelect() {
                signOut();
            },
        },
    ],
]);
</script>

<template>
    <UDropdownMenu
        :items="items"
        size="md"
        :ui="{
            content: 'w-52',
        }"
    >
        <UAvatar
            src="https://github.com/benjamincanac.png"
            size="md"
            class="cursor-pointer"
        />
    </UDropdownMenu>
</template>
