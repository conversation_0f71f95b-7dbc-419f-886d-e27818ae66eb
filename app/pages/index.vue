<script setup lang="ts">
</script>

<template>
    <div class="p-6">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                Dashboard
            </h1>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Sample dashboard cards -->
                <div
                    class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700"
                >
                    <div class="flex items-center">
                        <UIcon
                            name="i-lucide-users"
                            class="h-8 w-8 text-blue-500"
                        />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                Total Users
                            </p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                1,234
                            </p>
                        </div>
                    </div>
                </div>

                <div
                    class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700"
                >
                    <div class="flex items-center">
                        <UIcon
                            name="i-lucide-folder"
                            class="h-8 w-8 text-green-500"
                        />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                Active Projects
                            </p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                56
                            </p>
                        </div>
                    </div>
                </div>

                <div
                    class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700"
                >
                    <div class="flex items-center">
                        <UIcon
                            name="i-lucide-chart-line"
                            class="h-8 w-8 text-purple-500"
                        />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                Analytics Views
                            </p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                8,901
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Welcome to SenseHawk Console
                    </h2>
                </div>
                <div class="p-6">
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        You're successfully logged in to the SenseHawk Console. Use the sidebar navigation to explore
                        different sections of the application.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <UBadge
                            color="blue"
                            variant="soft"
                        >
                            Dashboard
                        </UBadge>
                        <UBadge
                            color="green"
                            variant="soft"
                        >
                            Projects
                        </UBadge>
                        <UBadge
                            color="purple"
                            variant="soft"
                        >
                            Analytics
                        </UBadge>
                        <UBadge
                            color="orange"
                            variant="soft"
                        >
                            Reports
                        </UBadge>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
